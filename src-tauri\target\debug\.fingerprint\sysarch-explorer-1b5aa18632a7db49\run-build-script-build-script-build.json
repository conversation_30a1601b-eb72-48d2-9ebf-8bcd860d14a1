{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1418850686435699030, "build_script_build", false, 18329512818067303202], [12092653563678505622, "build_script_build", false, 8512805929032627445], [16702348383442838006, "build_script_build", false, 6343360547497565935]], "local": [{"RerunIfChanged": {"output": "debug\\build\\sysarch-explorer-1b5aa18632a7db49\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}